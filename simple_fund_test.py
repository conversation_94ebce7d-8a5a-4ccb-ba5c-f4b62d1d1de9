#!/usr/bin/env python3
"""
Simple test script to debug fund creation issue.
"""

import json
import boto3
import requests
from botocore.exceptions import ClientError

# API Gateway configuration
api_base_url = "https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev"
api_headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
}

# Cognito configuration
cognito_client_id = "2jh76f894g6lv9vrus4qbb9hu7"
cognito_client = boto3.client("cognito-idp", region_name="ap-northeast-1")

def authenticate():
    """Authenticate with Cognito to get a valid JWT token"""
    print("🔐 Authenticating with AWS Cognito...")
    
    test_username = "<EMAIL>"
    test_password = "TestPassword123!"
    user_pool_id = "ap-northeast-1_H2kKHGUAT"
    
    try:
        # First, try to create the test user if it doesn't exist
        try:
            print("🔧 Creating test user if it doesn't exist...")
            cognito_client.admin_create_user(
                UserPoolId=user_pool_id,
                Username=test_username,
                TemporaryPassword=test_password,
                MessageAction="SUPPRESS",  # Don't send welcome email
                UserAttributes=[
                    {"Name": "email", "Value": test_username},
                    {"Name": "email_verified", "Value": "true"},
                ],
            )
            print("✅ Test user created successfully")

            # Set permanent password
            cognito_client.admin_set_user_password(
                UserPoolId=user_pool_id,
                Username=test_username,
                Password=test_password,
                Permanent=True,
            )
            print("✅ Test user password set")

        except ClientError as create_error:
            error_code = create_error.response.get("Error", {}).get("Code", "")
            if error_code == "UsernameExistsException":
                print("ℹ️  Test user already exists, proceeding with authentication")
            else:
                print(f"⚠️  Error creating test user: {create_error}")
                print("   Proceeding with authentication attempt...")
        
        # Try to authenticate
        print("🔑 Authenticating test user...")
        response = cognito_client.admin_initiate_auth(
            UserPoolId=user_pool_id,
            ClientId=cognito_client_id,
            AuthFlow="ADMIN_NO_SRP_AUTH",
            AuthParameters={"USERNAME": test_username, "PASSWORD": test_password},
        )
        
        if "AuthenticationResult" in response:
            access_token = response["AuthenticationResult"].get("AccessToken")
            if access_token:
                api_headers["Authorization"] = f"Bearer {access_token}"
                print("✅ Successfully authenticated")
                return True
                
    except ClientError as auth_error:
        print(f"❌ Authentication error: {auth_error}")
        return False
    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return False

def test_simple_fund_creation():
    """Test creating a very simple fund"""
    print("\n📝 Testing simple fund creation...")
    
    import time
    
    # Simple fund data with unique ID (keeping under 25 chars)
    fund_data = {
        "fund_id": f"TEST-{int(time.time())}",
        "name": "Simple Test Fund",
        "fund_type": "equity"
    }
    
    try:
        print(f"🔍 Sending request to: {api_base_url}/funds")
        print(f"📦 Request data: {json.dumps(fund_data, indent=2)}")
        print(f"🔑 Headers: {api_headers}")
        
        # Call API Gateway to create fund
        response = requests.post(
            f"{api_base_url}/funds",
            headers=api_headers,
            json=fund_data,
            timeout=30,
        )
        
        print(f"📊 Response status: {response.status_code}")
        print(f"📄 Response headers: {dict(response.headers)}")
        print(f"📄 Response body: {response.text}")
        
        if response.status_code in [200, 201]:
            print("✅ Fund created successfully")
            return True
        else:
            print(f"❌ Error creating fund: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error calling API: {e}")
        return False

def test_api_health():
    """Test if the API is responding"""
    print("\n🏥 Testing API health...")
    
    try:
        response = requests.get(
            f"{api_base_url}/health",
            headers={"Accept": "application/json"},
            timeout=10,
        )
        
        print(f"📊 Health check status: {response.status_code}")
        print(f"📄 Health check response: {response.text}")
        
        if response.status_code == 200:
            print("✅ API is healthy")
            return True
        else:
            print("❌ API health check failed")
            return False
            
    except Exception as e:
        print(f"❌ Error checking API health: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Starting simple fund creation test...")
    
    # Authenticate
    if not authenticate():
        print("❌ Authentication failed, stopping tests")
        return
    
    # Test fund creation
    test_simple_fund_creation()

if __name__ == "__main__":
    main()