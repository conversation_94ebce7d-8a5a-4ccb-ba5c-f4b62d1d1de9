#!/usr/bin/env python3
"""
Sample Data Population Script for FundFlow DynamoDB Tables

This script populates all DynamoDB tables with comprehensive sample data for testing and development.
DO NOT run this script if there is already data in the database as it will add duplicate records.

Usage:
    python populate_sample_data.py

Requirements:
    - AWS credentials configured (aws configure or environment variables)
    - DynamoDB tables already created (via SAM deployment)
    - Python 3.13 with required packages (boto3, decimal)

Environment:
    - Make sure you're using the ff_env conda environment: 
      export PATH="/opt/anaconda3/envs/ff_env/bin:$PATH"
"""

import boto3
import json
from decimal import Decimal
from datetime import datetime, timedelta
import uuid
import random

# AWS Configuration
AWS_REGION = 'ap-northeast-1'
ENVIRONMENT = 'dev'

# Table names
FUNDS_TABLE = f'fundflow-{ENVIRONMENT}-funds'
USERS_TABLE = f'fundflow-{ENVIRONMENT}-users'
PORTFOLIOS_TABLE = f'fundflow-{ENVIRONMENT}-portfolios'
REPORTS_TABLE = f'fundflow-{ENVIRONMENT}-reports'

# Initialize DynamoDB client
dynamodb = boto3.resource('dynamodb', region_name=AWS_REGION)

def decimal_default(obj):
    """JSON serializer for Decimal objects"""
    if isinstance(obj, Decimal):
        return float(obj)
    raise TypeError

def create_sample_funds():
    """Create comprehensive sample fund data"""
    
    fund_types = ['equity', 'debt', 'hybrid', 'index_fund', 'etf']
    risk_levels = ['very_low', 'low', 'moderate', 'high', 'very_high']
    categories = ['Large Cap Equity', 'Mid Cap Equity', 'Small Cap Equity', 'Debt', 'Hybrid', 'International', 'Sectoral', 'Tax Saving']
    fund_houses = ['HDFC', 'ICICI Prudential', 'SBI', 'Axis', 'Kotak', 'Aditya Birla', 'Franklin Templeton', 'DSP', 'Nippon India', 'UTI']
    
    funds = []
    
    for i in range(100):
        fund_house = random.choice(fund_houses)
        category = random.choice(categories)
        risk_level = random.choice(risk_levels)
        fund_type = random.choice(fund_types)
        
        # Generate realistic NAV and performance data
        base_nav = Decimal(str(round(random.uniform(10, 500), 2)))
        nav_change = Decimal(str(round(random.uniform(-5, 5), 2)))
        nav_change_pct = Decimal(str(round(random.uniform(-2, 2), 2)))
        
        fund_id = f"{fund_house.replace(' ', '').upper()}-{category.replace(' ', '').upper()}-{i+1:03d}"
        
        fund = {
            'fund_id': fund_id,
            'name': f"{fund_house} {category} Fund {i+1}",
            'fund_type': fund_type,
            'status': random.choice(['active', 'inactive']) if random.random() > 0.9 else 'active',
            'nav': base_nav,
            'nav_date': (datetime.now() - timedelta(days=random.randint(0, 5))).isoformat(),
            'previous_nav': base_nav - nav_change,
            'nav_change': nav_change,
            'nav_change_percentage': nav_change_pct,
            'total_assets': Decimal(str(round(random.uniform(500, 50000), 2))),  # AUM in crores
            'expense_ratio': Decimal(str(round(random.uniform(0.5, 2.5), 2))),
            'minimum_investment': Decimal(str(random.choice([500, 1000, 5000, 10000]))),
            'risk_level': risk_level,
            'inception_date': (datetime.now() - timedelta(days=random.randint(365, 3650))).isoformat(),
            'fund_manager': f"{random.choice(['Amit', 'Priya', 'Rajesh', 'Sneha', 'Vikram', 'Anita'])} {random.choice(['Sharma', 'Patel', 'Singh', 'Kumar', 'Gupta'])}",
            'benchmark': random.choice(['NIFTY 50', 'NIFTY 100', 'BSE SENSEX', 'NIFTY MIDCAP 100', 'CRISIL Composite Bond Fund Index']),
            'bloomberg_ticker': f"{fund_id}.BO",
            'isin': f"INF{random.randint(100000, 999999)}01{random.randint(100, 999)}",
            'description': f"This {fund_type} fund focuses on {category.lower()} investments with {risk_level} risk profile. Managed by experienced professionals.",
            'investment_objective': f"To provide long-term capital appreciation by investing in {category.lower()} securities.",
            
            # Performance metrics
            'performance_metrics': {
                'one_month_return': Decimal(str(round(random.uniform(-8, 12), 2))),
                'three_month_return': Decimal(str(round(random.uniform(-15, 20), 2))),
                'six_month_return': Decimal(str(round(random.uniform(-20, 25), 2))),
                'one_year_return': Decimal(str(round(random.uniform(-25, 35), 2))),
                'three_year_return': Decimal(str(round(random.uniform(-10, 50), 2))),
                'five_year_return': Decimal(str(round(random.uniform(0, 80), 2))),
                'since_inception_return': Decimal(str(round(random.uniform(5, 120), 2))),
                'volatility': Decimal(str(round(random.uniform(5, 25), 2))),
                'sharpe_ratio': Decimal(str(round(random.uniform(-0.5, 2.5), 2))),
                'sortino_ratio': Decimal(str(round(random.uniform(-0.5, 3.0), 2))),
                'information_ratio': Decimal(str(round(random.uniform(-1.0, 2.0), 2))),
                'treynor_ratio': Decimal(str(round(random.uniform(0.01, 0.20), 3))),
                'alpha': Decimal(str(round(random.uniform(-5, 10), 2))),
                'beta': Decimal(str(round(random.uniform(0.5, 1.5), 2))),
                'r_squared': Decimal(str(round(random.uniform(0.6, 0.99), 2))),
                'max_drawdown': Decimal(str(round(random.uniform(-40, -5), 2))),
                'calmar_ratio': Decimal(str(round(random.uniform(0.1, 2.0), 2))),
                'capture_ratio_up': Decimal(str(round(random.uniform(0.8, 1.2), 2))),
                'capture_ratio_down': Decimal(str(round(random.uniform(0.7, 1.1), 2))),
                'tracking_error': Decimal(str(round(random.uniform(1, 8), 2))),
                'jensen_alpha': Decimal(str(round(random.uniform(-3, 8), 2))),
            },
            
            # Holdings information
            'holdings': {
                'top_holdings': [
                    {
                        'name': 'Reliance Industries Ltd',
                        'symbol': 'RELIANCE',
                        'percentage': Decimal(str(round(random.uniform(5, 12), 2))),
                        'quantity': random.randint(10000, 100000),
                        'market_value': Decimal(str(round(random.uniform(500000, 2000000), 2))),
                        'sector': 'Oil & Gas'
                    },
                    {
                        'name': 'HDFC Bank Ltd',
                        'symbol': 'HDFCBANK',
                        'percentage': Decimal(str(round(random.uniform(4, 10), 2))),
                        'quantity': random.randint(8000, 80000),
                        'market_value': Decimal(str(round(random.uniform(400000, 1800000), 2))),
                        'sector': 'Banking'
                    },
                    {
                        'name': 'Infosys Ltd',
                        'symbol': 'INFY',
                        'percentage': Decimal(str(round(random.uniform(3, 9), 2))),
                        'quantity': random.randint(6000, 70000),
                        'market_value': Decimal(str(round(random.uniform(300000, 1600000), 2))),
                        'sector': 'Information Technology'
                    }
                ],
                'sector_allocation': {
                    'Financial Services': Decimal(str(round(random.uniform(20, 35), 1))),
                    'Information Technology': Decimal(str(round(random.uniform(15, 25), 1))),
                    'Consumer Goods': Decimal(str(round(random.uniform(8, 18), 1))),
                    'Healthcare': Decimal(str(round(random.uniform(5, 15), 1))),
                    'Energy': Decimal(str(round(random.uniform(5, 12), 1))),
                    'Industrial': Decimal(str(round(random.uniform(3, 10), 1))),
                    'Other': Decimal(str(round(random.uniform(5, 15), 1)))
                },
                'asset_allocation': {
                    'equity': Decimal(str(round(random.uniform(60, 95), 1))),
                    'debt': Decimal(str(round(random.uniform(2, 25), 1))),
                    'cash': Decimal(str(round(random.uniform(1, 8), 1))),
                    'other': Decimal(str(round(random.uniform(0, 5), 1)))
                }
            },
            
            # Custom fields for additional frontend data
            'custom_fields': {
                'category': category,
                'sub_category': random.choice(['Large Cap', 'Mid Cap', 'Small Cap', 'Multi Cap']),
                'volume': str(random.randint(10000, 1000000)),
                'rating': str(random.randint(1, 5)),
                'dividend_yield': str(round(random.uniform(0, 5), 2)),
                'portfolio_pe_ratio': str(round(random.uniform(10, 30), 2)),
                'portfolio_pb_ratio': str(round(random.uniform(1, 5), 2)),
                'fund_house': fund_house,
                'launch_date': (datetime.now() - timedelta(days=random.randint(365, 3650))).strftime('%Y-%m-%d'),
                'tax_implications': random.choice(['LTCG applicable', 'STCG applicable', 'Tax free']),
                'exit_load': f"{round(random.uniform(0, 2), 2)}% if redeemed within {random.randint(1, 12)} months",
                'sip_minimum': str(random.choice([500, 1000, 2000, 5000])),
                'swp_available': random.choice(['Yes', 'No']),
                'stp_available': random.choice(['Yes', 'No'])
            },
            
            # Timestamps
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat(),
            'last_nav_update': (datetime.now() - timedelta(days=random.randint(0, 2))).isoformat(),
            
            # GSI fields for querying
            'gsi1_pk': f"TYPE#{fund_type}",
            'gsi1_sk': f"RISK#{risk_level}",
            'gsi2_pk': f"HOUSE#{fund_house.replace(' ', '_')}",
            'gsi2_sk': f"CATEGORY#{category.replace(' ', '_')}"
        }
        
        funds.append(fund)
    
    return funds

def create_sample_users():
    """Create sample user data"""
    
    users = []
    user_names = [
        ('Amit', 'Sharma'), ('Priya', 'Patel'), ('Rajesh', 'Kumar'), ('Sneha', 'Singh'),
        ('Vikram', 'Gupta'), ('Anita', 'Mehta'), ('Rohit', 'Verma'), ('Kavita', 'Jain'),
        ('Suresh', 'Reddy'), ('Meera', 'Iyer'), ('Ashok', 'Agarwal'), ('Sunita', 'Rao'),
        ('Manish', 'Tiwari'), ('Ritu', 'Bansal'), ('Deepak', 'Malhotra')
    ]
    
    for i, (first_name, last_name) in enumerate(user_names):
        user_id = f"user-{i+1:03d}"
        email = f"{first_name.lower()}.{last_name.lower()}@example.com"
        
        user = {
            'user_id': user_id,
            'email': email,
            'first_name': first_name,
            'last_name': last_name,
            'full_name': f"{first_name} {last_name}",
            'phone': f"+91-{random.randint(7000000000, 9999999999)}",
            'date_of_birth': (datetime.now() - timedelta(days=random.randint(8000, 20000))).strftime('%Y-%m-%d'),
            'pan_number': f"{''.join(random.choices('ABCDEFGHIJKLMNOPQRSTUVWXYZ', k=5))}{random.randint(1000, 9999)}{''.join(random.choices('ABCDEFGHIJKLMNOPQRSTUVWXYZ', k=1))}",
            'address': {
                'street': f"{random.randint(1, 999)} {random.choice(['MG Road', 'Park Street', 'Brigade Road', 'Commercial Street', 'Residency Road'])}",
                'city': random.choice(['Mumbai', 'Delhi', 'Bangalore', 'Chennai', 'Kolkata', 'Pune', 'Hyderabad']),
                'state': random.choice(['Maharashtra', 'Delhi', 'Karnataka', 'Tamil Nadu', 'West Bengal', 'Telangana']),
                'postal_code': f"{random.randint(100000, 999999)}",
                'country': 'India'
            },
            'investment_profile': {
                'risk_tolerance': random.choice(['conservative', 'moderate', 'aggressive']),
                'investment_experience': random.choice(['beginner', 'intermediate', 'expert']),
                'annual_income': Decimal(str(random.randint(500000, 5000000))),
                'investment_horizon': random.choice(['short_term', 'medium_term', 'long_term']),
                'investment_objectives': random.choices([
                    'wealth_creation', 'tax_saving', 'retirement_planning', 
                    'child_education', 'emergency_fund', 'regular_income'
                ], k=random.randint(1, 3))
            },
            'kyc_status': 'verified',
            'account_status': 'active',
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat(),
            'last_login': (datetime.now() - timedelta(days=random.randint(0, 30))).isoformat(),
            'preferences': {
                'communication_channel': random.choice(['email', 'sms', 'both']),
                'language': random.choice(['english', 'hindi']),
                'statement_frequency': random.choice(['monthly', 'quarterly', 'annually']),
                'notifications': {
                    'price_alerts': random.choice([True, False]),
                    'portfolio_updates': True,
                    'market_news': random.choice([True, False]),
                    'transaction_alerts': True
                }
            }
        }
        
        users.append(user)
    
    return users

def create_sample_portfolios():
    """Create sample portfolio data"""
    
    portfolios = []
    portfolio_types = ['personal', 'retirement', 'education', 'emergency', 'tax_saving']
    
    # Create multiple portfolios for some users
    for user_idx in range(15):  # For 15 users
        user_id = f"user-{user_idx+1:03d}"
        num_portfolios = random.randint(1, 3)  # 1-3 portfolios per user
        
        for portfolio_idx in range(num_portfolios):
            portfolio_id = f"portfolio-{user_idx+1:03d}-{portfolio_idx+1}"
            portfolio_type = random.choice(portfolio_types)
            
            # Generate holdings (2-8 fund holdings per portfolio)
            holdings = []
            total_invested = Decimal('0')
            
            num_holdings = random.randint(2, 8)
            for holding_idx in range(num_holdings):
                fund_idx = random.randint(1, 100)
                fund_id = f"HDFC-LARGECAPEQUITY-{fund_idx:03d}"  # Reference to sample fund
                
                shares = Decimal(str(round(random.uniform(100, 5000), 4)))
                avg_cost = Decimal(str(round(random.uniform(10, 200), 2)))
                current_price = avg_cost * Decimal(str(round(random.uniform(0.8, 1.4), 2)))
                market_value = shares * current_price
                cost_basis = shares * avg_cost
                total_invested += cost_basis
                
                holding = {
                    'holding_id': f"{portfolio_id}-{holding_idx+1}",
                    'fund_id': fund_id,
                    'fund_name': f"Sample Fund {fund_idx}",
                    'fund_symbol': f"SF{fund_idx:03d}",
                    'shares': shares,
                    'average_cost': avg_cost,
                    'current_price': current_price,
                    'market_value': market_value,
                    'cost_basis': cost_basis,
                    'unrealized_gain_loss': market_value - cost_basis,
                    'unrealized_gain_loss_pct': ((market_value - cost_basis) / cost_basis * 100) if cost_basis > 0 else Decimal('0'),
                    'first_purchase_date': (datetime.now() - timedelta(days=random.randint(30, 1000))).isoformat(),
                    'last_updated': datetime.now().isoformat()
                }
                holdings.append(holding)
            
            # Calculate portfolio totals
            total_market_value = sum(h['market_value'] for h in holdings)
            total_cost_basis = sum(h['cost_basis'] for h in holdings)
            cash_balance = Decimal(str(round(random.uniform(1000, 50000), 2)))
            total_value = total_market_value + cash_balance
            
            # Generate transactions
            transactions = []
            for trans_idx in range(random.randint(5, 20)):
                holding = random.choice(holdings)
                transaction_type = random.choice(['buy', 'sell', 'dividend', 'sip'])
                
                transaction = {
                    'transaction_id': f"{portfolio_id}-T{trans_idx+1:03d}",
                    'fund_id': holding['fund_id'],
                    'fund_name': holding['fund_name'],
                    'fund_symbol': holding['fund_symbol'],
                    'transaction_type': transaction_type,
                    'transaction_date': (datetime.now() - timedelta(days=random.randint(1, 365))).isoformat(),
                    'shares': Decimal(str(round(random.uniform(10, 500), 4))) if transaction_type in ['buy', 'sell'] else None,
                    'price': Decimal(str(round(random.uniform(10, 200), 2))) if transaction_type in ['buy', 'sell'] else None,
                    'amount': Decimal(str(round(random.uniform(1000, 25000), 2))),
                    'fees': Decimal(str(round(random.uniform(0, 100), 2))),
                    'tax': Decimal(str(round(random.uniform(0, 500), 2))) if transaction_type == 'sell' else Decimal('0'),
                    'description': f"{transaction_type.title()} transaction for {holding['fund_name']}",
                    'reference_number': f"REF{random.randint(1000000, 9999999)}",
                    'created_at': datetime.now().isoformat()
                }
                transactions.append(transaction)
            
            # Calculate portfolio weights
            for holding in holdings:
                holding['weight'] = (holding['market_value'] / total_market_value * 100) if total_market_value > 0 else Decimal('0')
            
            portfolio = {
                'portfolio_id': portfolio_id,
                'user_id': user_id,
                'name': f"{portfolio_type.title().replace('_', ' ')} Portfolio {portfolio_idx+1}",
                'description': f"A {portfolio_type.replace('_', ' ')} focused investment portfolio",
                'portfolio_type': portfolio_type,
                'status': 'active',
                'base_currency': 'INR',
                'inception_date': (datetime.now() - timedelta(days=random.randint(90, 1000))).isoformat(),
                'total_value': total_value,
                'total_cost_basis': total_cost_basis,
                'cash_balance': cash_balance,
                'total_gain_loss': total_market_value - total_cost_basis,
                'total_gain_loss_pct': ((total_market_value - total_cost_basis) / total_cost_basis * 100) if total_cost_basis > 0 else Decimal('0'),
                'holdings': holdings,
                'recent_transactions': sorted(transactions, key=lambda x: x['transaction_date'], reverse=True)[:10],
                'performance': {
                    'total_return': total_market_value - total_cost_basis,
                    'total_return_pct': ((total_market_value - total_cost_basis) / total_cost_basis * 100) if total_cost_basis > 0 else Decimal('0'),
                    'one_month_return': Decimal(str(round(random.uniform(-5, 8), 2))),
                    'three_month_return': Decimal(str(round(random.uniform(-10, 15), 2))),
                    'six_month_return': Decimal(str(round(random.uniform(-15, 20), 2))),
                    'one_year_return': Decimal(str(round(random.uniform(-20, 30), 2))),
                    'inception_return': ((total_market_value - total_cost_basis) / total_cost_basis * 100) if total_cost_basis > 0 else Decimal('0'),
                    'volatility': Decimal(str(round(random.uniform(8, 25), 2))),
                    'sharpe_ratio': Decimal(str(round(random.uniform(0.5, 2.5), 2))),
                    'max_drawdown': Decimal(str(round(random.uniform(-25, -5), 2))),
                    'as_of_date': datetime.now().isoformat()
                },
                'risk_level': random.choice(['low', 'medium', 'high']),
                'benchmark': random.choice(['NIFTY 50', 'BSE SENSEX', 'NIFTY 100']),
                'rebalance_frequency': random.choice(['monthly', 'quarterly', 'semi_annually', 'annually']),
                'auto_rebalance': random.choice([True, False]),
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat(),
                'last_rebalanced': (datetime.now() - timedelta(days=random.randint(30, 180))).isoformat() if random.random() > 0.3 else None
            }
            
            portfolios.append(portfolio)
    
    return portfolios

def create_sample_reports():
    """Create sample report data"""
    
    reports = []
    report_types = ['performance', 'tax', 'holdings', 'transactions', 'risk_analysis', 'asset_allocation']
    
    for i in range(50):  # Create 50 sample reports
        user_id = f"user-{random.randint(1, 15):03d}"
        report_type = random.choice(report_types)
        
        report = {
            'report_id': f"report-{i+1:03d}",
            'user_id': user_id,
            'report_type': report_type,
            'title': f"{report_type.title().replace('_', ' ')} Report",
            'description': f"Comprehensive {report_type.replace('_', ' ')} analysis and insights",
            'status': random.choice(['generated', 'processing', 'failed']) if random.random() > 0.9 else 'generated',
            'file_format': random.choice(['pdf', 'excel', 'csv']),
            'file_size': random.randint(50000, 2000000),  # File size in bytes
            'parameters': {
                'start_date': (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d'),
                'end_date': datetime.now().strftime('%Y-%m-%d'),
                'include_charts': random.choice([True, False]),
                'include_benchmarks': random.choice([True, False]),
                'currency': 'INR',
                'portfolios': [f"portfolio-{random.randint(1, 15):03d}-{random.randint(1, 3)}"],
                'granularity': random.choice(['daily', 'weekly', 'monthly'])
            },
            'metadata': {
                'total_pages': random.randint(5, 50),
                'charts_count': random.randint(2, 15),
                'tables_count': random.randint(3, 20),
                'data_points': random.randint(100, 5000)
            },
            'download_url': f"/reports/{i+1:03d}/download",
            'expires_at': (datetime.now() + timedelta(days=30)).isoformat(),
            'created_at': (datetime.now() - timedelta(days=random.randint(0, 90))).isoformat(),
            'updated_at': datetime.now().isoformat(),
            'generated_by': 'system',
            'access_count': random.randint(0, 10)
        }
        
        reports.append(report)
    
    return reports

def populate_table(table_name, items, batch_size=25):
    """Populate a DynamoDB table with items in batches"""
    
    table = dynamodb.Table(table_name)
    print(f"📊 Populating {table_name} with {len(items)} items...")
    
    # Process items in batches
    for i in range(0, len(items), batch_size):
        batch = items[i:i + batch_size]
        
        with table.batch_writer() as batch_writer:
            for item in batch:
                try:
                    batch_writer.put_item(Item=item)
                except Exception as e:
                    print(f"❌ Error inserting item {item.get('fund_id', item.get('user_id', item.get('portfolio_id', item.get('report_id'))))}: {e}")
                    continue
        
        print(f"✅ Inserted batch {i//batch_size + 1}/{(len(items) + batch_size - 1)//batch_size}")
    
    print(f"🎉 Successfully populated {table_name}")

def verify_tables_exist():
    """Verify that all required tables exist"""
    
    tables_to_check = [FUNDS_TABLE, USERS_TABLE, PORTFOLIOS_TABLE, REPORTS_TABLE]
    
    for table_name in tables_to_check:
        try:
            table = dynamodb.Table(table_name)
            table.load()
            print(f"✅ Table {table_name} exists")
        except Exception as e:
            print(f"❌ Table {table_name} does not exist or is not accessible: {e}")
            return False
    
    return True

def main():
    """Main function to populate all sample data"""
    
    print("🚀 Starting FundFlow Sample Data Population")
    print("=" * 60)
    
    # Verify tables exist
    if not verify_tables_exist():
        print("\n❌ Some required tables are missing. Please run SAM deployment first.")
        print("Command: sam build && sam deploy --config-env dev")
        return
    
    print("\n📋 Generating sample data...")
    
    # Generate sample data
    print("🏦 Creating sample funds...")
    funds = create_sample_funds()
    
    print("👥 Creating sample users...")
    users = create_sample_users()
    
    print("📈 Creating sample portfolios...")
    portfolios = create_sample_portfolios()
    
    print("📊 Creating sample reports...")
    reports = create_sample_reports()
    
    print(f"\n📊 Data Summary:")
    print(f"  • Funds: {len(funds)}")
    print(f"  • Users: {len(users)}")
    print(f"  • Portfolios: {len(portfolios)}")
    print(f"  • Reports: {len(reports)}")
    
    # Confirm before proceeding
    response = input("\n⚠️  This will add sample data to your DynamoDB tables. Continue? (y/N): ")
    if response.lower() != 'y':
        print("❌ Operation cancelled.")
        return
    
    print("\n🔄 Populating tables...")
    
    # Populate tables
    try:
        populate_table(FUNDS_TABLE, funds)
        populate_table(USERS_TABLE, users)
        populate_table(PORTFOLIOS_TABLE, portfolios)
        populate_table(REPORTS_TABLE, reports)
        
        print("\n🎉 Sample data population completed successfully!")
        print("\n📋 What was created:")
        print(f"  • {len(funds)} mutual funds across different categories and risk levels")
        print(f"  • {len(users)} user accounts with complete profiles")
        print(f"  • {len(portfolios)} portfolios with holdings and transactions")
        print(f"  • {len(reports)} generated reports of various types")
        
        print("\n💡 Next steps:")
        print("  • Test the frontend application with the sample data")
        print("  • Use the fund search and filtering features")
        print("  • Create new portfolios and add holdings")
        print("  • Generate reports and analyze performance")
        
    except Exception as e:
        print(f"\n❌ Error during population: {e}")
        print("Please check your AWS credentials and table permissions.")

if __name__ == "__main__":
    main()