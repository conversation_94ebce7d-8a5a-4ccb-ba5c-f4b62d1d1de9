{"summary": {"total_tests": 10, "passed": 2, "failed": 8, "success_rate": "20.0%", "test_timestamp": "2025-07-10 22:08:19", "auth_available": true}, "test_results": [{"test_name": "Funds Health Check", "success": false, "message": "Unexpected status code: 401", "timestamp": "2025-07-10 22:08:10", "response_data": {"status_code": 401, "response": "{\"message\":\"Unauthorized\"}"}}, {"test_name": "List Funds", "success": true, "message": "Successfully retrieved 0 funds", "timestamp": "2025-07-10 22:08:14", "response_data": {"funds_count": 0, "sample_fund": null}}, {"test_name": "List Funds with Filter: {'fund_type': 'equity'}", "success": false, "message": "Failed to list funds with filter. Status: 500", "timestamp": "2025-07-10 22:08:14", "response_data": {"filter": {"fund_type": "equity"}, "status_code": 500}}, {"test_name": "List Funds with Filter: {'status': 'active'}", "success": true, "message": "Successfully retrieved 0 funds with filter", "timestamp": "2025-07-10 22:08:15", "response_data": {"filter": {"status": "active"}, "funds_count": 0}}, {"test_name": "List Funds with Filter: {'limit': '5'}", "success": true, "message": "Successfully retrieved 0 funds with filter", "timestamp": "2025-07-10 22:08:16", "response_data": {"filter": {"limit": "5"}, "funds_count": 0}}, {"test_name": "List Funds with Filter: {'fund_type': 'bond', 'status': 'active'}", "success": true, "message": "Successfully retrieved 0 funds with filter", "timestamp": "2025-07-10 22:08:16", "response_data": {"filter": {"fund_type": "bond", "status": "active"}, "funds_count": 0}}, {"test_name": "Get Fund by ID", "success": false, "message": "No funds available to test individual fund retrieval", "timestamp": "2025-07-10 22:08:17", "response_data": {}}, {"test_name": "Get Fund Market Data", "success": false, "message": "No funds available for market data test", "timestamp": "2025-07-10 22:08:17", "response_data": {}}, {"test_name": "Search Funds: 'equity'", "success": false, "message": "Search failed. Status: 404", "timestamp": "2025-07-10 22:08:18", "response_data": {"query": "equity", "status_code": 404}}, {"test_name": "Search Funds: 'bond'", "success": false, "message": "Search failed. Status: 404", "timestamp": "2025-07-10 22:08:18", "response_data": {"query": "bond", "status_code": 404}}, {"test_name": "Search Funds: 'growth'", "success": false, "message": "Search failed. Status: 404", "timestamp": "2025-07-10 22:08:18", "response_data": {"query": "growth", "status_code": 404}}, {"test_name": "Search Funds: 'income'", "success": false, "message": "Search failed. Status: 404", "timestamp": "2025-07-10 22:08:19", "response_data": {"query": "income", "status_code": 404}}, {"test_name": "Create Fund", "success": false, "message": "Failed to create fund. Status: 422", "timestamp": "2025-07-10 22:08:19", "response_data": {"status_code": 422, "response": "{\"error\": \"VALIDATION_ERROR\", \"message\": \"Invalid fund data\", \"details\": {\"validation_errors\": [{\"type\": \"missing\", \"loc\": [\"fund_id\"], \"msg\": \"Field required\", \"input\": {\"name\": \"Test Fund 1752156499\", \"fund_type\": \"equity\", \"status\": \"active\", \"description\": \"A test fund created by integration tests\", \"fund_manager\": \"Test Manager\", \"inception_date\": \"2024-01-01T00:00:00Z\", \"currency\": \"USD\", \"nav\": \"100.00\", \"total_assets\": \"1000000.00\", \"expense_ratio\": \"0.75\", \"minimum_investment\": \"1000.00\", \"risk_level\": \"moderate\"}, \"url\": \"https://errors.pydantic.dev/2.11/v/missing\"}]}, \"timestamp\": \"2025-07-10T14:08:19.537632+00:00\"}"}}, {"test_name": "Update Fund", "success": false, "message": "No created fund ID available for update test", "timestamp": "2025-07-10 22:08:19", "response_data": {}}, {"test_name": "Delete Fund", "success": false, "message": "No created fund ID available for delete test", "timestamp": "2025-07-10 22:08:19", "response_data": {}}, {"test_name": "Invalid Fund ID", "success": true, "message": "Correctly returned 404 for invalid fund ID", "timestamp": "2025-07-10 22:08:19", "response_data": {"fund_id": "invalid-fund-id-12345", "status_code": 404}}]}