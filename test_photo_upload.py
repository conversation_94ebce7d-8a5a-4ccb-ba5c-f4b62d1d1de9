#!/usr/bin/env python3
"""
Test script for fund manager photo upload functionality.
This script tests the complete photo upload flow from base64 to S3.
"""

import base64
import json
import requests
import os
from pathlib import Path

# Configuration
API_BASE_URL = "https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev"
FUND_ID = "test-fund-123"  # Replace with actual fund ID

def create_test_image_base64():
    """
    Create a simple test image in base64 format.
    This creates a 100x100 red square PNG image.
    """
    from PIL import Image, ImageDraw
    from io import BytesIO
    
    # Create a simple red square image
    img = Image.new('RGB', (100, 100), color='red')
    draw = ImageDraw.Draw(img)
    draw.text((10, 10), "TEST", fill='white')
    
    # Convert to base64
    buffer = BytesIO()
    img.save(buffer, format='PNG')
    img_data = buffer.getvalue()
    
    # Convert to base64 string
    base64_str = base64.b64encode(img_data).decode('utf-8')
    
    return f"data:image/png;base64,{base64_str}"

def get_auth_token():
    """
    Get authentication token for API calls.
    Note: This is a simplified example. In practice, you'd use proper OAuth flow.
    """
    # For testing, you would need to implement proper authentication
    # This is a placeholder - you'll need to replace with actual token
    return "YOUR_JWT_TOKEN_HERE"

def test_photo_upload():
    """Test the photo upload functionality."""
    try:
        print("🔧 Testing Fund Manager Photo Upload...")
        
        # Create test image
        print("📸 Creating test image...")
        test_image_base64 = create_test_image_base64()
        print(f"✅ Test image created (size: {len(test_image_base64)} characters)")
        
        # Prepare request data
        upload_data = {
            "image_data": test_image_base64,
            "filename": "test_manager_photo.png"
        }
        
        # Get auth token (you'll need to implement this)
        auth_token = get_auth_token()
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {auth_token}"
        }
        
        upload_url = f"{API_BASE_URL}/funds/{FUND_ID}/manager-photo"
        
        print(f"📤 Uploading photo to: {upload_url}")
        
        # Make upload request
        response = requests.post(upload_url, headers=headers, json=upload_data)
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📄 Response Body: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            photo_url = result.get("data", {}).get("photo_url")
            print(f"✅ Photo uploaded successfully!")
            print(f"📸 Photo URL: {photo_url}")
            
            # Test photo deletion
            print("\n🗑️ Testing photo deletion...")
            delete_response = requests.delete(upload_url, headers=headers)
            print(f"📊 Delete Response Status: {delete_response.status_code}")
            print(f"📄 Delete Response Body: {delete_response.text}")
            
            if delete_response.status_code == 200:
                print("✅ Photo deleted successfully!")
            else:
                print(f"❌ Photo deletion failed")
                
        else:
            print(f"❌ Photo upload failed")
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")

def test_s3_photo_manager_directly():
    """Test the S3PhotoManager directly (without API)."""
    try:
        print("\n🔧 Testing S3PhotoManager directly...")
        
        # Import the photo manager
        import sys
        sys.path.append('/Users/<USER>/2025/Projects/FundFlow/src')
        
        from shared.utils.s3_photo_manager import photo_manager
        
        # Create test image
        test_image_base64 = create_test_image_base64()
        
        # Test upload
        print("📤 Testing direct S3 upload...")
        result = photo_manager.upload_base64_photo(
            base64_data=test_image_base64,
            filename="direct_test.png",
            fund_id="test-fund-direct"
        )
        
        print(f"📊 Upload Result: {result}")
        
        if result["success"]:
            print("✅ Direct S3 upload successful!")
            photo_url = result["url"]
            print(f"📸 Photo URL: {photo_url}")
            
            # Test deletion
            print("🗑️ Testing direct S3 deletion...")
            deleted = photo_manager.delete_photo_by_url(photo_url)
            print(f"✅ Photo deleted: {deleted}")
            
        else:
            print(f"❌ Direct S3 upload failed: {result['error']}")
            
    except Exception as e:
        print(f"❌ Direct test failed: {str(e)}")

if __name__ == "__main__":
    print("🚀 Fund Manager Photo Upload Test")
    print("=" * 50)
    
    # Test S3PhotoManager directly first
    test_s3_photo_manager_directly()
    
    # Test via API (requires proper authentication)
    print("\n" + "=" * 50)
    print("⚠️  API test requires proper authentication token")
    print("Please update the get_auth_token() function with a valid JWT token")
    print("Uncomment the line below to test the API endpoint:")
    print("# test_photo_upload()")