#!/usr/bin/env python3
"""
Test the NAV update logic directly without API authentication.
"""
import os
import sys
from decimal import Decimal
from datetime import datetime, timezone

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_nav_update_logic():
    """Test NAV update logic directly."""
    try:
        from shared.models.fund import Fund
        from shared.models.market_data import MarketDataInput
        
        print("Testing NAV update logic...")
        
        # Create a mock fund
        fund = Fund(
            fund_id="TEST-001",
            name="Test Fund",
            symbol="TEST",
            fund_type="mutual_fund",
            nav=Decimal("100.00"),  # Original NAV
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )
        
        print(f"Original NAV: {fund.nav}")
        
        # Create market data input with new NAV
        market_data_input = MarketDataInput(
            fund_id="TEST-001",
            data_timestamp=datetime.now(timezone.utc),
            input_by="test_user",
            validated=True,
            nav=Decimal("125.50"),  # New NAV
            market_price=Decimal("125.25"),
            volume=1000,
            notes="Test NAV update"
        )
        
        print(f"New NAV from market data: {market_data_input.nav}")
        
        # Test the update logic from the fixed function
        if market_data_input.nav is not None:
            old_nav = fund.nav
            fund.nav = market_data_input.nav
            print(f"NAV updated: {old_nav} -> {fund.nav}")
            
        # Update timestamp
        fund.updated_at = datetime.now(timezone.utc)
        
        print(f"Final NAV: {fund.nav}")
        
        # Verify the update
        if fund.nav == Decimal("125.50"):
            print("✓ NAV update logic works correctly!")
            return True
        else:
            print(f"✗ NAV update failed. Expected: 125.50, Got: {fund.nav}")
            return False
            
    except Exception as e:
        print(f"✗ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_nav_update_logic()
    sys.exit(0 if success else 1)