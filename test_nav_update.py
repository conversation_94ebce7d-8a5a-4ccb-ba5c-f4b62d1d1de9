#!/usr/bin/env python3
"""
Test script to verify NAV update functionality.
"""
import os
import sys
import json
import requests
from datetime import datetime

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Test configuration
API_BASE_URL = "https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev"
FUND_ID = "FUND-001"  # Using a known fund ID

def test_nav_update():
    """Test NAV update via market data input."""
    print("Testing NAV update via market data input...")
    
    # Test data for market data input
    market_data = {
        "fundId": FUND_ID,
        "dataTimestamp": datetime.now().isoformat(),
        "inputBy": "test_user",
        "validated": True,
        "nav": 125.50,  # New NAV value
        "marketPrice": 125.25,
        "volume": 1000,
        "notes": "Test NAV update"
    }
    
    # Submit market data
    url = f"{API_BASE_URL}/funds/{FUND_ID}/market-data"
    
    # Create a mock event for testing
    event = {
        "httpMethod": "POST",
        "path": f"/funds/{FUND_ID}/market-data",
        "body": json.dumps(market_data),
        "headers": {"Content-Type": "application/json"},
        "pathParameters": {"fundId": FUND_ID}
    }
    
    print(f"Submitting market data to: {url}")
    print(f"Market data: {json.dumps(market_data, indent=2)}")
    
    try:
        response = requests.post(url, json=market_data)
        print(f"Response status: {response.status_code}")
        print(f"Response body: {response.text}")
        
        if response.status_code == 200:
            print("✓ Market data submitted successfully")
            
            # Now check if the fund's NAV was updated
            fund_url = f"{API_BASE_URL}/funds/{FUND_ID}"
            fund_response = requests.get(fund_url)
            
            if fund_response.status_code == 200:
                fund_data = fund_response.json()
                current_nav = fund_data.get("data", {}).get("nav")
                print(f"Current fund NAV: {current_nav}")
                
                if current_nav == 125.50:
                    print("✓ NAV update successful!")
                    return True
                else:
                    print(f"✗ NAV not updated. Expected: 125.50, Got: {current_nav}")
                    return False
            else:
                print(f"✗ Failed to fetch fund data: {fund_response.status_code}")
                return False
        else:
            print(f"✗ Failed to submit market data: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"✗ Error during test: {e}")
        return False

if __name__ == "__main__":
    success = test_nav_update()
    sys.exit(0 if success else 1)