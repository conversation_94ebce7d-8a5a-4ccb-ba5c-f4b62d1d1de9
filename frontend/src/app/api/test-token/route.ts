import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Token test endpoint called');

    // Get the session on the server side
    const session = await getServerSession(authOptions);
    
    if (!session?.accessToken) {
      return NextResponse.json({
        error: 'No access token found',
        hasSession: !!session,
      }, { status: 401 });
    }

    // Test the token against AWS API Gateway
    const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev';
    const testUrl = `${apiBaseUrl}/funds?page_size=1`;
    
    console.log('🧪 Testing token against:', testUrl);
    console.log('🧪 Token preview:', `${session.accessToken.substring(0, 50)}...`);
    
    const response = await fetch(testUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${session.accessToken}`,
      },
    });

    const responseText = await response.text();
    let responseData;
    try {
      responseData = JSON.parse(responseText);
    } catch {
      responseData = responseText;
    }

    console.log('🧪 API Gateway response:', {
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries()),
      body: responseData
    });

    return NextResponse.json({
      tokenTest: {
        success: response.ok,
        status: response.status,
        statusText: response.statusText,
        responseHeaders: Object.fromEntries(response.headers.entries()),
        responseBody: responseData,
        requestUrl: testUrl,
        tokenLength: session.accessToken.length,
        tokenPreview: `${session.accessToken.substring(0, 50)}...`,
        isExpired: session.expiresAt ? Date.now() >= session.expiresAt * 1000 : false,
        expiresAt: session.expiresAt,
        timeUntilExpiry: session.expiresAt ? Math.round((session.expiresAt * 1000 - Date.now()) / 60000) : null,
      },
      session: {
        provider: session.provider,
        userEmail: session.user?.email,
        hasAccessToken: !!session.accessToken,
        hasRefreshToken: !!session.refreshToken,
      }
    });

  } catch (error) {
    console.error('❌ Token test error:', error);
    return NextResponse.json(
      { 
        error: 'Token test failed',
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace'
      },
      { status: 500 }
    );
  }
}