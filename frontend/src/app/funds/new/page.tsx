'use client';

import { useState } from 'react';

import { useRouter } from 'next/navigation';

import FundEditForm from '@/components/funds/FundEditForm';
import { Toast } from '@/components/ui';
import { Card, Button } from '@/components/ui';
import { useTranslation } from '@/i18n/provider';
import { fundApi } from '@/lib/api';
import { useAppDispatch } from '@/store/hooks';
import { fetchFunds } from '@/store/slices/fundsSlice';
import { Fund } from '@/types';

export default function NewFundPage() {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { t } = useTranslation();

  const [saveLoading, setSaveLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showSuccessToast, setShowSuccessToast] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  const handleCreateFund = async (fundData: Partial<Fund>) => {
    try {
      setSaveLoading(true);
      setError(null);
      
      console.log('Creating new fund:', fundData);
      
      // Call API to create fund
      const response = await fundApi.createFund(fundData);

      console.log('Fund created successfully:', response.data);

      // Extract fund information from response
      const createdFund = response.data?.fund;
      const fundName = createdFund?.name || fundData.name || 'New Fund';

      console.log('Fund created successfully:', fundName);
      console.log('Created fund data:', response.data);

      // Show styled success toast notification
      setSuccessMessage(t('funds.fundCreatedSuccessfully', { fundName }));
      setShowSuccessToast(true);

      // Navigate to funds list after a short delay to allow user to see the toast
      setTimeout(() => {
        router.push('/funds');
      }, 2000);
      
    } catch (err) {
      console.error('Error creating fund:', err);
      console.error('Error details:', {
        message: err instanceof Error ? err.message : 'Unknown error',
        stack: err instanceof Error ? err.stack : undefined,
        response: (err as any)?.response?.data || 'No response data'
      });

      const errorMessage = err instanceof Error ? err.message : 'Failed to create fund';
      setError(errorMessage);
      // Remove the alert and let the error display in the form
    } finally {
      setSaveLoading(false);
    }
  };

  const handleCancel = () => {
    router.push('/funds');
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors" style={{ fontFamily: 'Lato, sans-serif' }}>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center gap-4 mb-4">
            <Button 
              variant="outline" 
              onClick={handleCancel}
              className="flex items-center gap-2"
            >
              ← {t('common.back')}
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {t('funds.createNewFund')}
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                {t('funds.createNewFundDescription')}
              </p>
            </div>
          </div>
        </div>

        {/* Create Form */}
        <Card>
          <Card.Header>
            <Card.Title>{t('funds.fundDetails')}</Card.Title>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              {t('funds.form.enterInfo')}
            </p>
          </Card.Header>
          <Card.Content>
            {error && (
              <div className="mb-4 p-3 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md">
                <p className="text-red-600 dark:text-red-300 text-sm">{error}</p>
              </div>
            )}
            
            <FundEditForm
              isEditing={false}
              onSubmit={handleCreateFund}
              onCancel={handleCancel}
              loading={saveLoading}
            />
          </Card.Content>
        </Card>
      </div>

      {/* Success Toast Notification */}
      {showSuccessToast && (
        <Toast
          message={successMessage}
          type="success"
          duration={4000}
          onClose={() => setShowSuccessToast(false)}
        />
      )}
    </div>
  );
}
