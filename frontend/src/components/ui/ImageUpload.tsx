'use client';

import { useCallback, useState, useRef } from 'react';

import {
  CloudUpload,
  Delete,
  PhotoCamera,
  Image as ImageIcon,
  CheckCircle,
  Error,
} from '@mui/icons-material';

import { Button } from '@/components/ui';
import { useTranslation } from '@/i18n/provider';

export interface UploadedImage {
  id: string;
  file: File;
  preview: string;
  status: 'pending' | 'uploading' | 'success' | 'error';
  progress: number;
  error?: string;
}

interface ImageUploadProps {
  onImageSelected: (image: UploadedImage | null) => void;
  onUpload?: (image: UploadedImage) => Promise<string>; // Returns URL after upload
  currentImageUrl?: string;
  maxFileSize?: number; // in MB
  acceptedImageTypes?: string[];
  disabled?: boolean;
  className?: string;
  placeholder?: string;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  onImageSelected,
  onUpload,
  currentImageUrl,
  maxFileSize = 5, // 5MB default for images
  acceptedImageTypes = ['.jpg', '.jpeg', '.png', '.gif', '.webp'],
  disabled = false,
  className = '',
  placeholder = 'Upload fund manager photo',
}) => {
  const { t } = useTranslation();
  const [isDragOver, setIsDragOver] = useState(false);
  const [currentImage, setCurrentImage] = useState<UploadedImage | null>(null);
  const [isRemoved, setIsRemoved] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const changeButtonFileInputRef = useRef<HTMLInputElement>(null);

  const validateFile = useCallback((file: File): string | null => {
    // Check file type
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (!acceptedImageTypes.includes(fileExtension)) {
      return `File type not supported. Please upload ${acceptedImageTypes.join(', ')} files only.`;
    }

    // Check file size
    const fileSizeMB = file.size / (1024 * 1024);
    if (fileSizeMB > maxFileSize) {
      return `File size too large. Maximum size is ${maxFileSize}MB.`;
    }

    return null;
  }, [acceptedImageTypes, maxFileSize]);

  const createImagePreview = useCallback((file: File): Promise<string> => {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        resolve(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    });
  }, []);

  const processFile = useCallback(async (file: File) => {
    const error = validateFile(file);
    if (error) {
      console.warn('Image upload error:', error);
      return;
    }

    const preview = await createImagePreview(file);
    const newImage: UploadedImage = {
      id: `${Date.now()}-${Math.random().toString(36).substring(2)}`,
      file,
      preview,
      status: 'pending',
      progress: 0,
    };

    setCurrentImage(newImage);
    setIsRemoved(false); // Reset removed state when new image is selected
    onImageSelected(newImage);
  }, [validateFile, createImagePreview, onImageSelected]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!disabled) {
      setIsDragOver(true);
    }
  }, [disabled]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);

    if (disabled) {return;}

    const droppedFiles = e.dataTransfer.files;
    if (droppedFiles.length > 0) {
      processFile(droppedFiles[0]); // Only take the first file
    }
  }, [disabled, processFile]);

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files;
    if (selectedFiles && selectedFiles.length > 0) {
      processFile(selectedFiles[0]);
    }
    // Reset input value to allow selecting the same file again
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [processFile]);

  const handleBrowseClick = useCallback(() => {
    if (!disabled && fileInputRef.current) {
      // Reset the input value to allow selecting the same file again
      fileInputRef.current.value = '';
      fileInputRef.current.click();
    }
  }, [disabled]);

  const handleRemoveImage = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setCurrentImage(null);
    setIsRemoved(true); // Mark as removed so we don't show currentImageUrl
    onImageSelected(null);
  }, [onImageSelected]);

  const handleUpload = useCallback(async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!currentImage || !onUpload) {return;}

    try {
      setCurrentImage(prev => prev ? { ...prev, status: 'uploading', progress: 0 } : null);
      
      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setCurrentImage(prev => {
          if (!prev || prev.status !== 'uploading') {return prev;}
          const newProgress = Math.min(prev.progress + 10, 90);
          return { ...prev, progress: newProgress };
        });
      }, 200);

      const uploadedUrl = await onUpload(currentImage);
      
      clearInterval(progressInterval);
      setCurrentImage(prev => prev ? { ...prev, status: 'success', progress: 100 } : null);
      
      return uploadedUrl;
    } catch (error: unknown) {
      setCurrentImage(prev => prev ? { 
        ...prev, 
        status: 'error', 
        error: error instanceof Error ? error.message : 'Upload failed' 
      } : null);
    }
  }, [currentImage, onUpload]);

  const getStatusIcon = (status: UploadedImage['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <Error className="w-5 h-5 text-red-500" />;
      case 'uploading':
        return (
          <div className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
        );
      default:
        return <ImageIcon className="w-5 h-5 text-gray-500" />;
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) {return '0 Bytes';}
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Show currentImage if it exists, otherwise show currentImageUrl (unless it was removed)
  // After successful upload, we might have both currentImage and currentImageUrl
  const displayImage = currentImage?.preview || (!isRemoved ? currentImageUrl : null);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Image Preview or Drop Zone */}
      {displayImage ? (
        <div className="relative">
          <div className="relative w-32 h-32 mx-auto rounded-lg overflow-hidden border-2 border-gray-300 dark:border-gray-600">
            <img
              src={displayImage}
              alt="Fund manager"
              className="w-full h-full object-cover"
            />
            {currentImage && currentImage.status !== 'success' && (
              <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
                {getStatusIcon(currentImage.status)}
              </div>
            )}
          </div>
          
          {/* Progress Bar */}
          {currentImage && currentImage.status === 'uploading' && (
            <div className="mt-2 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
              <div
                className="bg-blue-500 h-1 rounded-full transition-all duration-300"
                style={{ width: `${currentImage.progress}%` }}
              />
            </div>
          )}
          
          {/* Image Info */}
          {currentImage && (
            <div className="mt-2 text-center">
              <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                {currentImage.file.name}
              </p>
              <div className="flex items-center justify-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                <span>{formatFileSize(currentImage.file.size)}</span>
                {currentImage.status === 'uploading' && (
                  <span>• {currentImage.progress}%</span>
                )}
                {currentImage.error && (
                  <span className="text-red-500">• {currentImage.error}</span>
                )}
              </div>
            </div>
          )}
          
          {/* Action Buttons */}
          <div className="mt-3 flex justify-center space-x-2">
            {/* Hidden file input for Change button */}
            <input
              ref={changeButtonFileInputRef}
              type="file"
              accept={acceptedImageTypes.join(',')}
              onChange={handleFileSelect}
              className="hidden"
              disabled={disabled}
            />
            
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                // Use setTimeout to ensure the click happens after all event handling
                setTimeout(() => {
                  if (!disabled && changeButtonFileInputRef.current) {
                    changeButtonFileInputRef.current.value = '';
                    changeButtonFileInputRef.current.click();
                  }
                }, 0);
              }}
              disabled={disabled}
              className="flex items-center space-x-1"
            >
              <PhotoCamera className="w-4 h-4" />
              <span>Change</span>
            </Button>
            
            {currentImage && onUpload && currentImage.status === 'pending' && (
              <Button
                type="button"
                size="sm"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleUpload(e);
                }}
                disabled={disabled}
                className="flex items-center space-x-1"
              >
                <CloudUpload className="w-4 h-4" />
                <span>Upload</span>
              </Button>
            )}
            
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleRemoveImage(e);
              }}
              disabled={disabled}
              className="flex items-center space-x-1 text-red-500 hover:text-red-600"
            >
              <Delete className="w-4 h-4" />
              <span>Remove</span>
            </Button>
          </div>
        </div>
      ) : (
        <div
          className={`
            relative border-2 border-dashed rounded-lg p-6 text-center transition-colors duration-200
            ${isDragOver 
              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
              : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
            }
            ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
          `}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={handleBrowseClick}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept={acceptedImageTypes.join(',')}
            onChange={handleFileSelect}
            className="hidden"
            disabled={disabled}
          />
          
          <PhotoCamera className="w-8 h-8 text-gray-400 mx-auto mb-3" />
          
          <div className="space-y-1">
            <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
              {isDragOver ? 'Drop image here' : placeholder}
            </p>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              Drag and drop an image, or{' '}
              <span className="text-blue-600 dark:text-blue-400 font-medium">
                browse
              </span>{' '}
              to select
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Max {maxFileSize}MB. Supports {acceptedImageTypes.join(', ')} files.
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageUpload;