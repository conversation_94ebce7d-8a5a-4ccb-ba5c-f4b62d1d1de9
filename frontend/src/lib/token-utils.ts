/**
 * Utility functions for handling different token types based on environment
 */

import { Session } from 'next-auth';

/**
 * Get the appropriate token for API Gateway based on environment
 * - Production: Use ID token (required by Cognito User Pool authorizer)
 * - Development: Use access token (for local testing flexibility)
 */
export function getApiToken(session: Session): string | undefined {
  if (!session) return undefined;

  // In production, API Gateway expects ID token for Cognito User Pool authorizer
  if (process.env.NODE_ENV === 'production') {
    console.log('🏭 Production environment: Using ID token for API Gateway');
    return session.idToken || session.accessToken; // Fallback to access token if ID token not available
  }

  // In development, use access token for local testing
  console.log('🛠️ Development environment: Using access token for API Gateway');
  return session.accessToken;
}

/**
 * Get token preview for debugging (safe for logging)
 */
export function getTokenPreview(token: string | undefined): string {
  if (!token) return 'No token';
  return `${token.substring(0, 30)}...${token.substring(token.length - 10)}`;
}

/**
 * Check if we have the required token for the current environment
 */
export function hasValidTokenForEnvironment(session: Session): boolean {
  if (!session) return false;

  if (process.env.NODE_ENV === 'production') {
    // Production needs ID token
    return !!(session.idToken || session.accessToken);
  }

  // Development needs access token
  return !!session.accessToken;
}